import {
  UserListResponseType,
  UserDetailResponseType,
  UserStatusChangeResponseType,
  UserApprovalResponseType,
  OperatorApprovalResponseType,
  ComprehensiveUserManagementResponseType,
  BulkOperationResponseType,
} from './types';

// Response transformers - convert backend data to frontend format
export const transformUserListResponse = (data: any): UserListResponseType => {
  // Handle the actual backend response structure
  // Backend returns: { success, message, data: [...], pagination: {...} }
  // Frontend expects: { success, data: { users: [...], pagination: {...} }, message }

  const users = Array.isArray(data.data) ? data.data : data.data?.users || data.users || [];
  const backendPagination = data.pagination || data.data?.pagination || {
    total: users.length,
    page: 1,
    limit: users.length,
    totalPages: 1,
  };

  // Both frontend and backend use 1-based pagination
  const pagination = {
    total: backendPagination.total,
    page: backendPagination.page || 1,
    limit: backendPagination.limit || 20,
    pages: backendPagination.totalPages || Math.ceil((backendPagination.total || 0) / (backendPagination.limit || 20)),
  };

  return {
    success: data.success ?? true,
    data: {
      users: users.map((user: any) => ({
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        user_type: user.user_type,
        status: user.status,
        approval_status: user.approval_status,
        created_at: user.created_at,
        updated_at: user.updated_at,
      })),
      pagination,
    },
    message: data.message,
  };
};

export const transformUserDetailResponse = (data: any): UserDetailResponseType => {
  // Handle different response structures:
  // 1. Direct user object: { success, data: { user: {...} } }
  // 2. Nested structure: { success, data: { data: { user: {...} } } }
  // 3. Array response with single user: { success, data: [user] }

  let user: any;

  if (data.data?.user) {
    // Structure: { success, data: { user: {...} } }
    user = data.data.user;
  } else if (data.data?.data?.user) {
    // Structure: { success, data: { data: { user: {...} } } }
    user = data.data.data.user;
  } else if (Array.isArray(data.data) && data.data.length > 0) {
    // Structure: { success, data: [user] }
    user = data.data[0];
  } else if (data.data && typeof data.data === 'object' && data.data.id) {
    // Structure: { success, data: user }
    user = data.data;
  } else {
    throw new Error('Invalid user detail response structure');
  }

  return {
    success: data.success ?? true,
    data: {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        user_type: user.user_type,
        status: user.status,
        approval_status: user.approval_status,
        created_at: user.created_at,
        updated_at: user.updated_at,
        address: user.address,
        city: user.city,
        country: user.country,
        profile_image: user.profile_image,
        documents: user.documents,
        vehicles: user.vehicles,
        access_points: user.access_points,
      },
    },
    message: data.message,
  };
};

export const transformUserStatusChangeResponse = (data: any): UserStatusChangeResponseType => {
  return {
    success: data.success,
    data: {
      user: {
        id: data.data.user.id,
        status: data.data.user.status,
        updated_at: data.data.user.updated_at,
      },
    },
    message: data.message,
  };
};

export const transformUserApprovalResponse = (data: any): UserApprovalResponseType => {
  return {
    success: data.success,
    data: {
      user: {
        id: data.data.user.id,
        approval_status: data.data.user.approval_status,
        updated_at: data.data.user.updated_at,
      },
    },
    message: data.message,
  };
};

export const transformOperatorApprovalResponse = (data: any): OperatorApprovalResponseType => {
  return {
    success: data.success,
    data: {
      user: {
        id: data.data.user.id,
        user_type: data.data.user.user_type,
        approval_status: data.data.user.approval_status,
        updated_at: data.data.user.updated_at,
      },
    },
    message: data.message,
  };
};

export const transformComprehensiveUserManagementResponse = (data: any): ComprehensiveUserManagementResponseType => {
  return {
    success: data.success,
    data: {
      user: {
        id: data.data.user.id,
        name: data.data.user.name,
        email: data.data.user.email,
        phone: data.data.user.phone,
        user_type: data.data.user.user_type,
        status: data.data.user.status,
        approval_status: data.data.user.approval_status,
        updated_at: data.data.user.updated_at,
      },
    },
    message: data.message,
  };
};

export const transformBulkOperationResponse = (data: any): BulkOperationResponseType => {
  return {
    success: data.success,
    data: {
      processed: data.data.processed,
      successful: data.data.successful,
      failed: data.data.failed,
      users: data.data.users.map((user: any) => ({
        id: user.id,
        success: user.success,
        message: user.message,
      })),
    },
    message: data.message,
  };
};

/* eslint-disable sonarjs/no-duplicate-string */
export const API_ENDPOINT = {
  // Admin Auth endpoints
  auth: {
    login: '/login',
    register: '/register',
    logout: '/logout',
    forgotPassword: '/forgot-password',
    resetPassword: '/reset-password',
    verifyOtp: '/verify-otp',
    changePassword: '/change-password',
  },

  // Admin Profile endpoints
  profile: '/profile',

  // Admin Management endpoints
  admins: {
    list: '/all',
    status: '/status',
  },

  // User Management endpoints
  users: {
    list: '/users',
    detail: (id: string) => `/users/${id}`, // Dedicated detail endpoint
    byId: (id: string) => `/users/${id}`,
    status: '/users',
    approval: '/users',
    management: '/users/management',
    bulkApprove: '/users/bulk-approve',
    bulkReject: '/users/bulk-reject',
  },

  // Shipment Management endpoints
  shipments: {
    expiredStats: '/shipments/expired-stats',
  },

  // Notifications endpoints
  notifications: {
    list: '/notifications',
    unreadCount: '/notifications/unread-count',
    markAsRead: (id: string) => `/notifications/${id}/read`,
    markAllAsRead: '/notifications/mark-all-read',
    preferences: '/notifications/preferences',
    filterOptions: '/notifications/filter-options',
    // Broadcast message endpoints
    broadcast: {
      send: '/notifications/broadcast/send',
      schedule: '/notifications/broadcast/schedule',
      preview: '/notifications/broadcast/preview',
      recipients: '/notifications/broadcast/recipients',
      validateRecipients: '/notifications/broadcast/validate-recipients',
    },
    // Template management endpoints
    templates: {
      list: '/notifications/templates',
      byId: (id: string) => `/notifications/templates/${id}`,
      create: '/notifications/templates',
      update: (id: string) => `/notifications/templates/${id}`,
      delete: (id: string) => `/notifications/templates/${id}`,
      duplicate: (id: string) => `/notifications/templates/${id}/duplicate`,
      usage: (id: string) => `/notifications/templates/${id}/usage`,
      preview: (id: string) => `/notifications/templates/${id}/preview`,
    },
  },

  // Dashboard endpoints
  dashboard: '/dashboard',
};
